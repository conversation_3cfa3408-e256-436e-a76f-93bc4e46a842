import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Button, Badge } from '../../atoms';
import { cn } from '../../../utils/cn';

interface PublicMapData {
  id: string;
  title: string;
  description?: string;
  base_map_type: string;
  feature_count?: number;
  view_count?: number;
  like_count?: number;
  is_featured?: boolean;
  created_at: string;
  created_by?: {
    name: string;
    avatar?: string;
  };
  thumbnail_url?: string;
}

interface PublicMapCardProps {
  map: PublicMapData;
  onView: (mapId: string) => void;
  onSave?: (mapId: string) => void;
  onLike?: (mapId: string) => void;
  className?: string;
  isLiked?: boolean;
  isSaved?: boolean;
}

const PublicMapCard: React.FC<PublicMapCardProps> = ({
  map,
  onView,
  onSave,
  onLike,
  className,
  isLiked = false,
  isSaved = false,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [liked, setLiked] = useState(isLiked);
  const [saved, setSaved] = useState(isSaved);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getBaseMapIcon = (baseMapType: string) => {
    switch (baseMapType) {
      case 'satellite':
        return '🛰️';
      case 'terrain':
        return '🏔️';
      case 'dark':
        return '🌙';
      default:
        return '🗺️';
    }
  };

  const handleLike = async () => {
    if (!onLike) return;
    
    setIsLoading(true);
    try {
      await onLike(map.id);
      setLiked(!liked);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    if (!onSave) return;
    
    setIsLoading(true);
    try {
      await onSave(map.id);
      setSaved(!saved);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={cn(
      'bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200',
      className
    )}>
      {/* Map Thumbnail */}
      <div className="relative h-48 bg-gray-100 rounded-t-lg overflow-hidden">
        {map.thumbnail_url ? (
          <img
            src={map.thumbnail_url}
            alt={map.title}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-100 to-green-100">
            <div className="text-center">
              <div className="text-4xl mb-2">{getBaseMapIcon(map.base_map_type)}</div>
              <Typography variant="caption" color="secondary">
                {map.base_map_type.charAt(0).toUpperCase() + map.base_map_type.slice(1)} Map
              </Typography>
            </div>
          </div>
        )}
        
        {/* Featured Badge */}
        {map.is_featured && (
          <div className="absolute top-3 left-3">
            <Badge variant="warning" size="sm">
              ⭐ Featured
            </Badge>
          </div>
        )}

        {/* Action Buttons */}
        <div className="absolute top-3 right-3 flex space-x-2">
          {onLike && (
            <button
              onClick={handleLike}
              disabled={isLoading}
              className={cn(
                'p-2 rounded-lg shadow-sm transition-all duration-200',
                liked
                  ? 'bg-red-500 text-white hover:bg-red-600'
                  : 'bg-white bg-opacity-90 hover:bg-opacity-100 text-gray-600 hover:text-red-500'
              )}
            >
              <svg className="w-4 h-4" fill={liked ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
            </button>
          )}
          
          {onSave && (
            <button
              onClick={handleSave}
              disabled={isLoading}
              className={cn(
                'p-2 rounded-lg shadow-sm transition-all duration-200',
                saved
                  ? 'bg-blue-500 text-white hover:bg-blue-600'
                  : 'bg-white bg-opacity-90 hover:bg-opacity-100 text-gray-600 hover:text-blue-500'
              )}
            >
              <svg className="w-4 h-4" fill={saved ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
              </svg>
            </button>
          )}
        </div>
      </div>

      {/* Map Info */}
      <div className="p-4">
        <div className="mb-3">
          <Typography variant="h6" color="gray" className="font-semibold mb-1 truncate">
            {map.title}
          </Typography>
          {map.description && (
            <Typography variant="body2" color="secondary" className="line-clamp-2">
              {map.description}
            </Typography>
          )}
        </div>

        {/* Creator Info */}
        {map.created_by && (
          <div className="flex items-center space-x-2 mb-3">
            {map.created_by.avatar ? (
              <img
                src={map.created_by.avatar}
                alt={map.created_by.name}
                className="w-6 h-6 rounded-full"
              />
            ) : (
              <div className="w-6 h-6 rounded-full bg-gray-300 flex items-center justify-center">
                <span className="text-xs font-medium text-gray-600">
                  {map.created_by.name.charAt(0).toUpperCase()}
                </span>
              </div>
            )}
            <Typography variant="caption" color="secondary">
              by {map.created_by.name}
            </Typography>
          </div>
        )}

        {/* Map Stats */}
        <div className="flex items-center space-x-4 mb-3 text-sm text-gray-500">
          <div className="flex items-center space-x-1">
            <span>📍</span>
            <span>{map.feature_count || 0} features</span>
          </div>
          <div className="flex items-center space-x-1">
            <span>👁️</span>
            <span>{map.view_count || 0} views</span>
          </div>
          <div className="flex items-center space-x-1">
            <span>❤️</span>
            <span>{map.like_count || 0} likes</span>
          </div>
        </div>

        {/* Meta Info */}
        <div className="mb-4">
          <Typography variant="caption" color="secondary">
            Created {formatDate(map.created_at)}
          </Typography>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-2">
          <Button
            variant="primary"
            size="sm"
            onClick={() => onView(map.id)}
            className="flex-1"
            disabled={isLoading}
          >
            View Map
          </Button>
          {onSave && (
            <Button
              variant={saved ? "primary" : "secondary"}
              size="sm"
              onClick={handleSave}
              disabled={isLoading}
              className={saved ? "bg-blue-600 hover:bg-blue-700" : ""}
            >
              {saved ? '💾 Saved' : '💾 Save'}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default PublicMapCard;
