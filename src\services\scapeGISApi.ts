// ScapeGIS API Service for Maps and Projects
class ScapeGISAPI {
  private baseURL: string;
  private token: string | null;

  constructor() {
    this.baseURL = 'http://localhost:8001/api/v1';
    this.token = localStorage.getItem('access_token');
  }

  private async request(endpoint: string, options: RequestInit = {}): Promise<any> {
    const url = `${this.baseURL}${endpoint}`;
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(this.token && { 'Authorization': `Bearer ${this.token}` }),
        ...options.headers
      },
      ...options
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `API Error: ${response.status}`);
      }
      
      return response.json();
    } catch (error) {
      console.error(`API request failed: ${endpoint}`, error);
      throw error;
    }
  }

  // Update token when it changes
  updateToken(token: string | null) {
    this.token = token;
  }

  // Maps API Methods
  async getMaps(page: number = 1, perPage: number = 10) {
    return this.request(`/maps/?page=${page}&per_page=${perPage}`);
  }

  async getPublicMaps(page: number = 1, perPage: number = 10) {
    return this.request(`/maps/public?page=${page}&per_page=${perPage}`);
  }

  async getMapById(mapId: string) {
    return this.request(`/maps/${mapId}`);
  }

  async createMap(mapData: {
    title: string;
    description?: string;
    base_map_type?: string;
    center?: { lat: number; lng: number };
    zoom_level?: number;
    is_public?: boolean;
  }) {
    return this.request('/maps/', {
      method: 'POST',
      body: JSON.stringify(mapData)
    });
  }

  async updateMap(mapId: string, mapData: Partial<{
    title: string;
    description: string;
    base_map_type: string;
    center: { lat: number; lng: number };
    zoom_level: number;
    is_public: boolean;
  }>) {
    return this.request(`/maps/${mapId}`, {
      method: 'PUT',
      body: JSON.stringify(mapData)
    });
  }

  async deleteMap(mapId: string) {
    return this.request(`/maps/${mapId}`, {
      method: 'DELETE'
    });
  }

  async duplicateMap(mapId: string) {
    return this.request(`/maps/${mapId}/duplicate`, {
      method: 'POST'
    });
  }

  // Map Features API Methods
  async getMapFeatures(mapId: string) {
    return this.request(`/maps/${mapId}/features`);
  }

  async addFeature(mapId: string, featureData: {
    name: string;
    description?: string;
    feature_type: 'marker' | 'polygon' | 'line';
    geometry: any;
    properties?: any;
  }) {
    return this.request(`/maps/${mapId}/features`, {
      method: 'POST',
      body: JSON.stringify(featureData)
    });
  }

  async updateFeature(mapId: string, featureId: string, featureData: any) {
    return this.request(`/maps/${mapId}/features/${featureId}`, {
      method: 'PUT',
      body: JSON.stringify(featureData)
    });
  }

  async deleteFeature(mapId: string, featureId: string) {
    return this.request(`/maps/${mapId}/features/${featureId}`, {
      method: 'DELETE'
    });
  }

  // Projects API Methods
  async getProjects(page: number = 1, perPage: number = 10) {
    return this.request(`/projects-webgis/?page=${page}&per_page=${perPage}`);
  }

  async getProjectById(projectId: string) {
    return this.request(`/projects-webgis/${projectId}`);
  }

  async createProject(projectData: {
    name: string;
    description?: string;
    is_public?: boolean;
  }) {
    return this.request('/projects-webgis/', {
      method: 'POST',
      body: JSON.stringify(projectData)
    });
  }

  async updateProject(projectId: string, projectData: Partial<{
    name: string;
    description: string;
    is_public: boolean;
  }>) {
    return this.request(`/projects-webgis/${projectId}`, {
      method: 'PUT',
      body: JSON.stringify(projectData)
    });
  }

  async deleteProject(projectId: string) {
    return this.request(`/projects-webgis/${projectId}`, {
      method: 'DELETE'
    });
  }

  async duplicateProject(projectId: string) {
    return this.request(`/projects-webgis/${projectId}/duplicate`, {
      method: 'POST'
    });
  }

  // Project Layers API Methods
  async getProjectLayers(projectId: string) {
    return this.request(`/projects-webgis/${projectId}/layers`);
  }

  async addProjectLayer(projectId: string, layerData: any) {
    return this.request(`/projects-webgis/${projectId}/layers`, {
      method: 'POST',
      body: JSON.stringify(layerData)
    });
  }

  // Map Statistics and Social Features
  async likeMap(mapId: string) {
    return this.request(`/maps/${mapId}/like`, {
      method: 'POST'
    });
  }

  async unlikeMap(mapId: string) {
    return this.request(`/maps/${mapId}/unlike`, {
      method: 'POST'
    });
  }

  async getMapStats(mapId: string) {
    return this.request(`/maps/${mapId}/stats`);
  }

  // Search functionality
  async searchMaps(query: string, filters?: {
    is_public?: boolean;
    created_by?: string;
    tags?: string[];
  }) {
    const params = new URLSearchParams({
      q: query,
      ...(filters?.is_public !== undefined && { is_public: filters.is_public.toString() }),
      ...(filters?.created_by && { created_by: filters.created_by }),
      ...(filters?.tags && { tags: filters.tags.join(',') })
    });

    return this.request(`/maps/search?${params.toString()}`);
  }

  async searchProjects(query: string, filters?: {
    is_public?: boolean;
    created_by?: string;
  }) {
    const params = new URLSearchParams({
      q: query,
      ...(filters?.is_public !== undefined && { is_public: filters.is_public.toString() }),
      ...(filters?.created_by && { created_by: filters.created_by })
    });

    return this.request(`/projects-webgis/search?${params.toString()}`);
  }
}

// Create and export a singleton instance
export const scapeGISApi = new ScapeGISAPI();

// Export the class for testing or custom instances
export default ScapeGISAPI;
