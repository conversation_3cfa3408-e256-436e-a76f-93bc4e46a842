import React, { useState } from 'react';
import { Button } from '../../atoms';
import { cn } from '../../../utils/cn';

interface FloatingActionButtonsProps {
  onCreateMap: () => void;
  onCreateProject: () => void;
  className?: string;
}

const FloatingActionButtons: React.FC<FloatingActionButtonsProps> = ({
  onCreateMap,
  onCreateProject,
  className,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const handleCreateMap = () => {
    setIsExpanded(false);
    onCreateMap();
  };

  const handleCreateProject = () => {
    setIsExpanded(false);
    onCreateProject();
  };

  return (
    <div className={cn('fixed bottom-6 right-6 z-40', className)}>
      {/* Secondary Action Buttons */}
      <div className={cn(
        'flex flex-col space-y-3 mb-3 transition-all duration-300 transform origin-bottom',
        isExpanded 
          ? 'opacity-100 scale-100 translate-y-0' 
          : 'opacity-0 scale-95 translate-y-2 pointer-events-none'
      )}>
        {/* Create Project Button */}
        <div className="flex items-center space-x-3">
          <div className="bg-black bg-opacity-75 text-white px-3 py-1 rounded-lg text-sm font-medium whitespace-nowrap">
            Create Project
          </div>
          <button
            onClick={handleCreateProject}
            className="w-12 h-12 bg-purple-500 hover:bg-purple-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center group"
          >
            <svg className="w-6 h-6 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
          </button>
        </div>

        {/* Create Map Button */}
        <div className="flex items-center space-x-3">
          <div className="bg-black bg-opacity-75 text-white px-3 py-1 rounded-lg text-sm font-medium whitespace-nowrap">
            Create Map
          </div>
          <button
            onClick={handleCreateMap}
            className="w-12 h-12 bg-green-500 hover:bg-green-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center group"
          >
            <svg className="w-6 h-6 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
            </svg>
          </button>
        </div>
      </div>

      {/* Main Action Button */}
      <button
        onClick={toggleExpanded}
        className={cn(
          'w-14 h-14 bg-primary-500 hover:bg-primary-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center',
          isExpanded && 'rotate-45'
        )}
      >
        <svg className="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
        </svg>
      </button>

      {/* Backdrop */}
      {isExpanded && (
        <div
          className="fixed inset-0 bg-black bg-opacity-20 z-[-1]"
          onClick={() => setIsExpanded(false)}
        />
      )}
    </div>
  );
};

export default FloatingActionButtons;
