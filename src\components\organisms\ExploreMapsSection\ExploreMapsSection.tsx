import React, { useState, useMemo } from 'react';
import { Typography, Button, Input } from '../../atoms';
import { PublicMapCard } from '../../molecules';
import { cn } from '../../../utils/cn';

interface PublicMapData {
  id: string;
  title: string;
  description?: string;
  base_map_type: string;
  feature_count?: number;
  view_count?: number;
  like_count?: number;
  is_featured?: boolean;
  created_at: string;
  created_by?: {
    name: string;
    avatar?: string;
  };
  thumbnail_url?: string;
}

interface ExploreMapsSection {
  maps: PublicMapData[];
  isLoading?: boolean;
  onRefresh: () => void;
  onViewMap: (mapId: string) => void;
  onSaveMap: (mapId: string) => void;
  onLikeMap: (mapId: string) => void;
  className?: string;
}

const ExploreMapsSection: React.FC<ExploreMapsSection> = ({
  maps,
  isLoading = false,
  onRefresh,
  onViewMap,
  onSaveMap,
  onLikeMap,
  className,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'created_at' | 'view_count' | 'like_count' | 'title'>('created_at');
  const [filterBy, setFilterBy] = useState<'all' | 'featured' | 'popular'>('all');

  const filteredAndSortedMaps = useMemo(() => {
    let filtered = maps;

    // Apply search filter
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      filtered = maps.filter(map => 
        map.title.toLowerCase().includes(searchLower) ||
        map.description?.toLowerCase().includes(searchLower) ||
        map.created_by?.name.toLowerCase().includes(searchLower)
      );
    }

    // Apply category filters
    if (filterBy === 'featured') {
      filtered = filtered.filter(map => map.is_featured);
    } else if (filterBy === 'popular') {
      filtered = filtered.filter(map => (map.view_count || 0) > 100 || (map.like_count || 0) > 10);
    }

    // Apply sorting
    return filtered.sort((a, b) => {
      switch (sortBy) {
        case 'title':
          return a.title.localeCompare(b.title);
        case 'view_count':
          return (b.view_count || 0) - (a.view_count || 0);
        case 'like_count':
          return (b.like_count || 0) - (a.like_count || 0);
        case 'created_at':
        default:
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      }
    });
  }, [maps, searchTerm, sortBy, filterBy]);

  if (isLoading) {
    return (
      <div className={cn('p-6', className)}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('p-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <Typography variant="h4" color="gray" className="font-bold mb-2">
            Explore Public Maps
          </Typography>
          <Typography variant="body2" color="secondary">
            Discover amazing maps created by the community
          </Typography>
        </div>
        
        <Button
          variant="secondary"
          size="sm"
          onClick={onRefresh}
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          Refresh
        </Button>
      </div>

      {/* Search and Filters */}
      <div className="mb-6 space-y-4">
        {/* Search Bar */}
        <div className="max-w-md">
          <Input
            type="text"
            placeholder="Search maps, descriptions, or creators..."
            value={searchTerm}
            onChange={setSearchTerm}
            className="w-full"
          />
        </div>

        {/* Filters and Sorting */}
        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Typography variant="body2" color="gray" className="font-medium">
                Show:
              </Typography>
              <select
                value={filterBy}
                onChange={(e) => setFilterBy(e.target.value as any)}
                className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="all">All Maps</option>
                <option value="featured">Featured Maps</option>
                <option value="popular">Popular Maps</option>
              </select>
            </div>
            
            <div className="flex items-center space-x-2">
              <Typography variant="body2" color="gray" className="font-medium">
                Sort by:
              </Typography>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="created_at">Recently Added</option>
                <option value="view_count">Most Viewed</option>
                <option value="like_count">Most Liked</option>
                <option value="title">Name</option>
              </select>
            </div>
          </div>
          
          <Typography variant="caption" color="secondary">
            {filteredAndSortedMaps.length} {filteredAndSortedMaps.length === 1 ? 'map' : 'maps'} found
          </Typography>
        </div>
      </div>

      {/* Maps Grid */}
      {filteredAndSortedMaps.length === 0 ? (
        <div className="text-center py-12">
          <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <Typography variant="h6" color="gray" className="mb-2">
            {searchTerm.trim() ? 'No maps found' : 'No public maps available'}
          </Typography>
          <Typography variant="body2" color="secondary" className="mb-6">
            {searchTerm.trim() 
              ? 'Try adjusting your search terms or filters'
              : 'Check back later for new public maps from the community'
            }
          </Typography>
          {searchTerm.trim() && (
            <Button 
              variant="secondary" 
              onClick={() => {
                setSearchTerm('');
                setFilterBy('all');
              }}
            >
              Clear Search
            </Button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredAndSortedMaps.map((map) => (
            <PublicMapCard
              key={map.id}
              map={map}
              onView={onViewMap}
              onSave={onSaveMap}
              onLike={onLikeMap}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default ExploreMapsSection;
