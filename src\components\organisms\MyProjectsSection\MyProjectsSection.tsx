import React from 'react';
import { Typo<PERSON>, Button } from '../../atoms';
import { ProjectCard } from '../../molecules';
import { cn } from '../../../utils/cn';

interface ProjectData {
  id: string;
  name: string;
  description?: string;
  is_public: boolean;
  created_at: string;
  updated_at: string;
  layer_count?: number;
}

interface MyProjectsSectionProps {
  projects: ProjectData[];
  isLoading?: boolean;
  onRefresh: () => void;
  onCreateProject: () => void;
  onViewProject: (projectId: string) => void;
  onEditProject: (projectId: string) => void;
  onDeleteProject: (projectId: string) => void;
  onDuplicateProject: (projectId: string) => void;
  className?: string;
}

const MyProjectsSection: React.FC<MyProjectsSectionProps> = ({
  projects,
  isLoading = false,
  onRefresh,
  onCreateProject,
  onViewProject,
  onEditProject,
  onDeleteProject,
  onDuplicateProject,
  className,
}) => {
  if (isLoading) {
    return (
      <div className={cn('p-6', className)}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('p-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <Typography variant="h4" color="gray" className="font-bold mb-2">
            My Projects
          </Typography>
          <Typography variant="body2" color="secondary">
            {projects.length} {projects.length === 1 ? 'project' : 'projects'} created
          </Typography>
        </div>
        
        <div className="flex items-center space-x-3">
          <Button
            variant="secondary"
            size="sm"
            onClick={onRefresh}
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Refresh
          </Button>
          
          <Button
            variant="primary"
            size="sm"
            onClick={onCreateProject}
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            Create Project
          </Button>
        </div>
      </div>

      {/* Projects Grid */}
      {projects.length === 0 ? (
        <div className="text-center py-12">
          <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
          </div>
          <Typography variant="h6" color="gray" className="mb-2">
            No projects yet
          </Typography>
          <Typography variant="body2" color="secondary" className="mb-6">
            Create your first GIS project to manage complex datasets and layers
          </Typography>
          <Button variant="primary" onClick={onCreateProject}>
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            Create Your First Project
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {projects.map((project) => (
            <ProjectCard
              key={project.id}
              project={project}
              onView={onViewProject}
              onEdit={onEditProject}
              onDelete={onDeleteProject}
              onDuplicate={onDuplicateProject}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default MyProjectsSection;
