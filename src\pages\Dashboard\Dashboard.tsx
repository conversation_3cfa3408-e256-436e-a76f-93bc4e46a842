import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { DashboardTemplate } from '../../components/templates';
import { MyMapsSection, MyProjectsSection, ExploreMapsSection } from '../../components/organisms';
import { FloatingActionButtons } from '../../components/molecules';
import { scapeGISApi } from '../../services/scapeGISApi';
import { useAuthStore } from '../../stores/authStore';

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [showOAuthSuccess, setShowOAuthSuccess] = useState(false);
  const [showOAuthError, setShowOAuthError] = useState(false);
  const [oauthErrorMessage, setOauthErrorMessage] = useState('');

  // Tab state
  const [activeTab, setActiveTab] = useState<'my-maps' | 'my-projects' | 'explore'>('my-maps');

  // Data states
  const [maps, setMaps] = useState<any[]>([]);
  const [projects, setProjects] = useState<any[]>([]);
  const [publicMaps, setPublicMaps] = useState<any[]>([]);
  const [isLoadingMaps, setIsLoadingMaps] = useState(false);
  const [isLoadingProjects, setIsLoadingProjects] = useState(false);
  const [isLoadingPublicMaps, setIsLoadingPublicMaps] = useState(false);

  // Auth store - use proper auth state management
  const { user, isAuthenticated, checkAuth } = useAuthStore();

  // Check authentication on component mount
  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isAuthenticated && user === null) {
      navigate('/login', { replace: true });
    }
  }, [isAuthenticated, user, navigate]);

  // Load data on mount and when authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      loadUserData();
      loadPublicMaps();
    }
  }, [isAuthenticated, user]);

  // Show success notification if user just logged in via OAuth
  useEffect(() => {
    const oauthSuccess = searchParams.get('oauth_success');
    const provider = searchParams.get('provider');

    if (oauthSuccess === 'true' && provider && user) {
      console.log(`✅ Login successful with ${provider}!`);
      setShowOAuthSuccess(true);

      // Hide success message after 5 seconds
      setTimeout(() => setShowOAuthSuccess(false), 5000);

      // Clean up URL parameters
      const newUrl = window.location.pathname;
      window.history.replaceState({}, '', newUrl);
    }
  }, [searchParams, user]);

  // Data loading functions
  const loadUserData = async () => {
    try {
      // Update API token
      scapeGISApi.updateToken(localStorage.getItem('access_token'));

      // Load user's maps
      setIsLoadingMaps(true);
      const mapsResponse = await scapeGISApi.getMaps();
      setMaps(mapsResponse.maps || []);

      // Load user's projects
      setIsLoadingProjects(true);
      const projectsResponse = await scapeGISApi.getProjects();
      setProjects(projectsResponse.projects || []);
    } catch (error) {
      console.error('Failed to load user data:', error);
      // Set empty arrays on error
      setMaps([]);
      setProjects([]);
    } finally {
      setIsLoadingMaps(false);
      setIsLoadingProjects(false);
    }
  };

  const loadPublicMaps = async () => {
    try {
      setIsLoadingPublicMaps(true);
      const response = await scapeGISApi.getPublicMaps();
      setPublicMaps(response.maps || []);
    } catch (error) {
      console.error('Failed to load public maps:', error);
      setPublicMaps([]);
    } finally {
      setIsLoadingPublicMaps(false);
    }
  };

  // Handle OAuth error parameters
  useEffect(() => {
    const oauthError = searchParams.get('oauth_error');
    const errorProvider = searchParams.get('provider');
    const errorMessage = searchParams.get('message');

    if (oauthError === 'true') {
      console.log(`❌ OAuth error for ${errorProvider}:`, errorMessage);
      setShowOAuthError(true);
      setOauthErrorMessage(`${errorProvider} login failed: ${errorMessage || 'Unknown error'}`);

      // Clean URL parameters
      setTimeout(() => {
        window.history.replaceState({}, document.title, '/dashboard');
      }, 100);

      // Hide error message after 8 seconds
      setTimeout(() => setShowOAuthError(false), 8000);
    }
  }, [searchParams]);

  // Create handlers
  const handleCreateMap = async () => {
    try {
      const newMap = await scapeGISApi.createMap({
        title: 'Untitled Map',
        description: 'A new map created with ScapeGIS',
        base_map_type: 'street',
        center: { lat: -6.2088, lng: 106.8456 }, // Jakarta
        zoom_level: 10,
        is_public: false
      });

      // Navigate to map editor
      navigate(`/maps/${newMap.id}/edit`);
    } catch (error) {
      console.error('Failed to create map:', error);
      alert('Failed to create map. Please try again.');
    }
  };

  const handleCreateProject = async () => {
    try {
      const newProject = await scapeGISApi.createProject({
        name: 'Untitled Project',
        description: 'A new GIS project created with ScapeGIS',
        is_public: false
      });

      // Navigate to project editor
      navigate(`/projects/${newProject.id}/edit`);
    } catch (error) {
      console.error('Failed to create project:', error);
      alert('Failed to create project. Please try again.');
    }
  };

  // Map handlers
  const handleViewMap = (mapId: string) => {
    navigate(`/maps/${mapId}`);
  };

  const handleEditMap = (mapId: string) => {
    navigate(`/maps/${mapId}/edit`);
  };

  const handleDeleteMap = async (mapId: string) => {
    if (window.confirm('Are you sure you want to delete this map?')) {
      try {
        await scapeGISApi.deleteMap(mapId);
        loadUserData(); // Refresh data
      } catch (error) {
        console.error('Failed to delete map:', error);
        alert('Failed to delete map. Please try again.');
      }
    }
  };

  const handleDuplicateMap = async (mapId: string) => {
    try {
      await scapeGISApi.duplicateMap(mapId);
      loadUserData(); // Refresh data
    } catch (error) {
      console.error('Failed to duplicate map:', error);
      alert('Failed to duplicate map. Please try again.');
    }
  };

  const handleShareMap = (mapId: string) => {
    // TODO: Implement share functionality
    console.log('Share map:', mapId);
  };

  // Project handlers
  const handleViewProject = (projectId: string) => {
    navigate(`/projects/${projectId}`);
  };

  const handleEditProject = (projectId: string) => {
    navigate(`/projects/${projectId}/edit`);
  };

  const handleDeleteProject = async (projectId: string) => {
    if (window.confirm('Are you sure you want to delete this project?')) {
      try {
        await scapeGISApi.deleteProject(projectId);
        loadUserData(); // Refresh data
      } catch (error) {
        console.error('Failed to delete project:', error);
        alert('Failed to delete project. Please try again.');
      }
    }
  };

  const handleDuplicateProject = async (projectId: string) => {
    try {
      await scapeGISApi.duplicateProject(projectId);
      loadUserData(); // Refresh data
    } catch (error) {
      console.error('Failed to duplicate project:', error);
      alert('Failed to duplicate project. Please try again.');
    }
  };

  // Public maps handlers
  const handleSaveMap = async (mapId: string) => {
    // TODO: Implement save to bookmarks functionality
    console.log('Save map:', mapId);
  };

  const handleLikeMap = async (mapId: string) => {
    try {
      await scapeGISApi.likeMap(mapId);
      loadPublicMaps(); // Refresh data
    } catch (error) {
      console.error('Failed to like map:', error);
    }
  };

  // Convert user to dashboard template format
  const dashboardUser = user ? {
    name: user.name || user.email || 'User',
    email: user.email || '',
    avatar: user.avatar,
    workspace: user.workspace || 'Personal Workspace'
  } : undefined;

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header with Tabs */}
        <div className="bg-white border-b border-gray-200">
          <div className="px-6 py-4">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Dashboard</h1>

            {/* OAuth Success Notification */}
            {showOAuthSuccess && (
              <div className="mb-4">
                <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-md shadow-sm">
                  <div className="flex items-center">
                    <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span>Login successful! Welcome, {user?.name || user?.email || 'User'}.</span>
                  </div>
                </div>
              </div>
            )}

            {/* OAuth Error Notification */}
            {showOAuthError && (
              <div className="mb-4">
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-md shadow-sm">
                  <div className="flex items-center">
                    <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                    <div>
                      <strong>Error!</strong> {oauthErrorMessage}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Navigation Tabs */}
            <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
              <button
                onClick={() => setActiveTab('my-maps')}
                className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                  activeTab === 'my-maps'
                    ? 'bg-white text-primary-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                My Maps ({maps.length})
              </button>
              <button
                onClick={() => setActiveTab('my-projects')}
                className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                  activeTab === 'my-projects'
                    ? 'bg-white text-primary-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                My Projects ({projects.length})
              </button>
              <button
                onClick={() => setActiveTab('explore')}
                className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                  activeTab === 'explore'
                    ? 'bg-white text-primary-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Explore Public Maps
              </button>
            </div>
          </div>
        </div>

        {/* Content Area */}
        <div className="flex-1 overflow-auto">
          {activeTab === 'my-maps' && (
            <MyMapsSection
              maps={maps}
              isLoading={isLoadingMaps}
              onRefresh={loadUserData}
              onCreateMap={handleCreateMap}
              onViewMap={handleViewMap}
              onEditMap={handleEditMap}
              onDeleteMap={handleDeleteMap}
              onDuplicateMap={handleDuplicateMap}
              onShareMap={handleShareMap}
            />
          )}

          {activeTab === 'my-projects' && (
            <MyProjectsSection
              projects={projects}
              isLoading={isLoadingProjects}
              onRefresh={loadUserData}
              onCreateProject={handleCreateProject}
              onViewProject={handleViewProject}
              onEditProject={handleEditProject}
              onDeleteProject={handleDeleteProject}
              onDuplicateProject={handleDuplicateProject}
            />
          )}

          {activeTab === 'explore' && (
            <ExploreMapsSection
              maps={publicMaps}
              isLoading={isLoadingPublicMaps}
              onRefresh={loadPublicMaps}
              onViewMap={handleViewMap}
              onSaveMap={handleSaveMap}
              onLikeMap={handleLikeMap}
            />
          )}
        </div>
      </div>

      {/* Floating Action Buttons */}
      <FloatingActionButtons
        onCreateMap={handleCreateMap}
        onCreateProject={handleCreateProject}
      />
    </div>
  );
};

export default Dashboard;
