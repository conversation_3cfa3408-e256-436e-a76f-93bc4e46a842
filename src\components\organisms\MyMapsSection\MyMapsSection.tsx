import React, { useState } from 'react';
import { <PERSON>po<PERSON>, Button } from '../../atoms';
import { MapCard } from '../../molecules';
import { cn } from '../../../utils/cn';

interface MapData {
  id: string;
  title: string;
  description?: string;
  base_map_type: string;
  center?: { lat: number; lng: number };
  zoom_level?: number;
  is_public: boolean;
  feature_count?: number;
  view_count?: number;
  like_count?: number;
  created_at: string;
  updated_at: string;
  thumbnail_url?: string;
}

interface MyMapsSectionProps {
  maps: MapData[];
  isLoading?: boolean;
  onRefresh: () => void;
  onCreateMap: () => void;
  onViewMap: (mapId: string) => void;
  onEditMap: (mapId: string) => void;
  onDeleteMap: (mapId: string) => void;
  onDuplicateMap: (mapId: string) => void;
  onShareMap: (mapId: string) => void;
  className?: string;
}

const MyMapsSection: React.FC<MyMapsSectionProps> = ({
  maps,
  isLoading = false,
  onRefresh,
  onCreateMap,
  onViewMap,
  onEditMap,
  onDeleteMap,
  onDuplicateMap,
  onShareMap,
  className,
}) => {
  const [sortBy, setSortBy] = useState<'created_at' | 'updated_at' | 'title'>('updated_at');
  const [filterBy, setFilterBy] = useState<'all' | 'public' | 'private'>('all');

  const filteredAndSortedMaps = React.useMemo(() => {
    let filtered = maps;

    // Apply filters
    if (filterBy === 'public') {
      filtered = maps.filter(map => map.is_public);
    } else if (filterBy === 'private') {
      filtered = maps.filter(map => !map.is_public);
    }

    // Apply sorting
    return filtered.sort((a, b) => {
      if (sortBy === 'title') {
        return a.title.localeCompare(b.title);
      }
      return new Date(b[sortBy]).getTime() - new Date(a[sortBy]).getTime();
    });
  }, [maps, sortBy, filterBy]);

  if (isLoading) {
    return (
      <div className={cn('p-6', className)}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('p-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <Typography variant="h4" color="gray" className="font-bold mb-2">
            My Maps
          </Typography>
          <Typography variant="body2" color="secondary">
            {maps.length} {maps.length === 1 ? 'map' : 'maps'} created
          </Typography>
        </div>
        
        <div className="flex items-center space-x-3">
          <Button
            variant="secondary"
            size="sm"
            onClick={onRefresh}
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Refresh
          </Button>
          
          <Button
            variant="primary"
            size="sm"
            onClick={onCreateMap}
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            Create Map
          </Button>
        </div>
      </div>

      {/* Filters and Sorting */}
      {maps.length > 0 && (
        <div className="flex items-center justify-between mb-6 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Typography variant="body2" color="gray" className="font-medium">
                Filter:
              </Typography>
              <select
                value={filterBy}
                onChange={(e) => setFilterBy(e.target.value as any)}
                className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="all">All Maps</option>
                <option value="public">Public Maps</option>
                <option value="private">Private Maps</option>
              </select>
            </div>
            
            <div className="flex items-center space-x-2">
              <Typography variant="body2" color="gray" className="font-medium">
                Sort by:
              </Typography>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="updated_at">Last Updated</option>
                <option value="created_at">Date Created</option>
                <option value="title">Name</option>
              </select>
            </div>
          </div>
          
          <Typography variant="caption" color="secondary">
            Showing {filteredAndSortedMaps.length} of {maps.length} maps
          </Typography>
        </div>
      )}

      {/* Maps Grid */}
      {filteredAndSortedMaps.length === 0 ? (
        <div className="text-center py-12">
          <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
            </svg>
          </div>
          <Typography variant="h6" color="gray" className="mb-2">
            {maps.length === 0 ? 'No maps yet' : 'No maps match your filters'}
          </Typography>
          <Typography variant="body2" color="secondary" className="mb-6">
            {maps.length === 0 
              ? 'Create your first map to get started with ScapeGIS'
              : 'Try adjusting your filters to see more maps'
            }
          </Typography>
          {maps.length === 0 && (
            <Button variant="primary" onClick={onCreateMap}>
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              Create Your First Map
            </Button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredAndSortedMaps.map((map) => (
            <MapCard
              key={map.id}
              map={map}
              onView={onViewMap}
              onEdit={onEditMap}
              onDelete={onDeleteMap}
              onDuplicate={onDuplicateMap}
              onShare={onShareMap}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default MyMapsSection;
