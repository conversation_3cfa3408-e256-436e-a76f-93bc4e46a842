import React from 'react';
import { Modal } from '../../atoms';
import { Button, Typography } from '../../atoms';

interface LogoutModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  loading?: boolean;
  userName?: string;
}

const LogoutModal: React.FC<LogoutModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  loading = false,
  userName,
}) => {
  const handleConfirm = () => {
    onConfirm();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="sm"
      closeOnBackdrop={!loading}
      showCloseButton={!loading}
    >
      <div className="text-center">
        {/* Icon */}
        <div className="mx-auto flex items-center justify-center w-12 h-12 rounded-full bg-red-100 mb-4">
          <svg
            className="w-6 h-6 text-red-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
            />
          </svg>
        </div>

        {/* Title */}
        <Typography variant="h6" color="gray" className="mb-2">
          Confirm Logout
        </Typography>

        {/* Message */}
        <Typography variant="body2" color="secondary" className="mb-6">
          {userName 
            ? `Are you sure you want to logout, ${userName}? You will need to sign in again to access your account.`
            : 'Are you sure you want to logout? You will need to sign in again to access your account.'
          }
        </Typography>

        {/* Actions */}
        <div className="flex space-x-3 justify-center">
          <Button
            variant="secondary"
            onClick={onClose}
            disabled={loading}
            className="min-w-[100px]"
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleConfirm}
            loading={loading}
            disabled={loading}
            className="min-w-[100px] bg-red-600 hover:bg-red-700 focus:ring-red-500"
          >
            {loading ? 'Logging out...' : 'Logout'}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default LogoutModal;
