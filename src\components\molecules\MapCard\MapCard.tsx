import React, { useState } from 'react';
import { <PERSON>po<PERSON>, <PERSON><PERSON>, Badge } from '../../atoms';
import { cn } from '../../../utils/cn';

interface MapData {
  id: string;
  title: string;
  description?: string;
  base_map_type: string;
  center?: { lat: number; lng: number };
  zoom_level?: number;
  is_public: boolean;
  feature_count?: number;
  view_count?: number;
  like_count?: number;
  created_at: string;
  updated_at: string;
  thumbnail_url?: string;
}

interface MapCardProps {
  map: MapData;
  onView: (mapId: string) => void;
  onEdit: (mapId: string) => void;
  onDelete: (mapId: string) => void;
  onDuplicate?: (mapId: string) => void;
  onShare?: (mapId: string) => void;
  className?: string;
  showActions?: boolean;
}

const MapCard: React.FC<MapCardProps> = ({
  map,
  onView,
  onEdit,
  onDelete,
  onDuplicate,
  onShare,
  className,
  showActions = true,
}) => {
  const [showDropdown, setShowDropdown] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getBaseMapIcon = (baseMapType: string) => {
    switch (baseMapType) {
      case 'satellite':
        return '🛰️';
      case 'terrain':
        return '🏔️';
      case 'dark':
        return '🌙';
      default:
        return '🗺️';
    }
  };

  const handleAction = async (action: () => void) => {
    setIsLoading(true);
    setShowDropdown(false);
    try {
      await action();
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={cn(
      'bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200',
      className
    )}>
      {/* Map Thumbnail */}
      <div className="relative h-48 bg-gray-100 rounded-t-lg overflow-hidden">
        {map.thumbnail_url ? (
          <img
            src={map.thumbnail_url}
            alt={map.title}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-100 to-green-100">
            <div className="text-center">
              <div className="text-4xl mb-2">{getBaseMapIcon(map.base_map_type)}</div>
              <Typography variant="caption" color="secondary">
                {map.base_map_type.charAt(0).toUpperCase() + map.base_map_type.slice(1)} Map
              </Typography>
            </div>
          </div>
        )}
        
        {/* Visibility Badge */}
        <div className="absolute top-3 left-3">
          <Badge
            variant={map.is_public ? 'success' : 'default'}
            size="sm"
          >
            {map.is_public ? '🌍 Public' : '🔒 Private'}
          </Badge>
        </div>

        {/* Actions Dropdown */}
        {showActions && (
          <div className="absolute top-3 right-3">
            <div className="relative">
              <button
                onClick={() => setShowDropdown(!showDropdown)}
                className="p-2 bg-white bg-opacity-90 hover:bg-opacity-100 rounded-lg shadow-sm transition-all duration-200"
                disabled={isLoading}
              >
                <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                </svg>
              </button>

              {/* Dropdown Menu */}
              {showDropdown && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                  <div className="py-1">
                    <button
                      onClick={() => handleAction(() => onEdit(map.id))}
                      className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                      <span>Edit</span>
                    </button>
                    
                    {onDuplicate && (
                      <button
                        onClick={() => handleAction(() => onDuplicate(map.id))}
                        className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                        <span>Duplicate</span>
                      </button>
                    )}
                    
                    {onShare && (
                      <button
                        onClick={() => handleAction(() => onShare(map.id))}
                        className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                        </svg>
                        <span>Share</span>
                      </button>
                    )}
                    
                    <div className="border-t border-gray-100 my-1"></div>
                    
                    <button
                      onClick={() => handleAction(() => onDelete(map.id))}
                      className="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                      <span>Delete</span>
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Map Info */}
      <div className="p-4">
        <div className="mb-3">
          <Typography variant="h6" color="gray" className="font-semibold mb-1 truncate">
            {map.title}
          </Typography>
          {map.description && (
            <Typography variant="body2" color="secondary" className="line-clamp-2">
              {map.description}
            </Typography>
          )}
        </div>

        {/* Map Stats */}
        <div className="flex items-center space-x-4 mb-3 text-sm text-gray-500">
          <div className="flex items-center space-x-1">
            <span>📍</span>
            <span>{map.feature_count || 0} features</span>
          </div>
          <div className="flex items-center space-x-1">
            <span>👁️</span>
            <span>{map.view_count || 0} views</span>
          </div>
          <div className="flex items-center space-x-1">
            <span>❤️</span>
            <span>{map.like_count || 0} likes</span>
          </div>
        </div>

        {/* Meta Info */}
        <div className="flex items-center justify-between mb-4">
          <Typography variant="caption" color="secondary">
            Created {formatDate(map.created_at)}
          </Typography>
          <Typography variant="caption" color="secondary">
            Updated {formatDate(map.updated_at)}
          </Typography>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-2">
          <Button
            variant="primary"
            size="sm"
            onClick={() => onView(map.id)}
            className="flex-1"
            disabled={isLoading}
          >
            View Map
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => onEdit(map.id)}
            disabled={isLoading}
          >
            Edit
          </Button>
        </div>
      </div>

      {/* Click outside to close dropdown */}
      {showDropdown && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setShowDropdown(false)}
        />
      )}
    </div>
  );
};

export default MapCard;
